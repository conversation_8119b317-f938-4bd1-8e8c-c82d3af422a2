[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "neutrosophic-renewable-forecasting"
version = "1.0.0"
description = "Neutrosophic Dual Clustering Random Forest Framework for Renewable Energy Forecasting"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "Research Team", email = "<EMAIL>"},
]
keywords = [
    "renewable energy",
    "forecasting",
    "neutrosophic sets",
    "clustering",
    "random forest",
    "uncertainty quantification"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
dependencies = [
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "scikit-learn>=1.0.0",
    "scikit-fuzzy>=0.4.2",
    "scipy>=1.7.0",
    "matplotlib>=3.4.0",
    "seaborn>=0.11.0",
    "plotly>=5.0.0",
    "pyyaml>=6.0",
    "requests>=2.25.0",
    "tqdm>=4.62.0",
    "joblib>=1.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.2.0",
    "pytest-cov>=3.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.910",
    "pre-commit>=2.15.0",
]
notebooks = [
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0",
]
experiments = [
    "optuna>=3.0.0",
    "mlflow>=2.0.0",
    "statsmodels>=0.13.0",
    "prophet>=1.1.0",
]

[project.urls]
Homepage = "https://github.com/example/neutrosophic-renewable-forecasting"
Documentation = "https://neutrosophic-renewable-forecasting.readthedocs.io/"
Repository = "https://github.com/example/neutrosophic-renewable-forecasting.git"
"Bug Tracker" = "https://github.com/example/neutrosophic-renewable-forecasting/issues"

[project.scripts]
neutro-forecast = "scripts.train_model:main"
neutro-evaluate = "scripts.evaluate_model:main"

[tool.setuptools]
package-dir = {"" = "."}

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]
exclude = ["tests*"]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "sklearn.*",
    "scipy.*",
    "matplotlib.*",
    "seaborn.*",
    "plotly.*",
    "skfuzzy.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]