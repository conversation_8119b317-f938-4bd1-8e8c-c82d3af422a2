# Neutrosophic Dual Clustering Random Forest Framework for Renewable Energy Forecasting

This repository implements the novel hybrid framework proposed in "A Neutrosophic Dual Clustering Random Forest Framework for Uncertainty-Aware Renewable Energy Forecasting" targeting IEEE Transactions on Neural Networks and Learning Systems (TNNLS).

## Overview

The framework combines:
- **Dual Clustering**: K-Means and Fuzzy C-Means for regime identification
- **Neutrosophic Set Theory**: Uncertainty quantification through Truth, Indeterminacy, and Falsity components
- **Random Forest Regression**: Ensemble learning for robust forecasting
- **Prediction Intervals**: Uncertainty-aware forecasting with confidence bounds

## Key Features

- Modular, extensible architecture
- Comprehensive evaluation framework
- Support for solar and wind power forecasting
- Ablation study capabilities
- Statistical significance testing
- Scalability analysis tools

## Installation

```bash
git clone <repository-url>
cd renewable_energy_forecasting
pip install -e .
```

## Quick Start

```python
from src.framework.forecasting_framework import NeutrosophicForecastingFramework
from src.data.data_loader import ENTSOEDataLoader

# Load data
loader = ENTSOEDataLoader()
data = loader.load_solar_data("2019-01-01", "2023-10-03")

# Initialize framework
framework = NeutrosophicForecastingFramework()

# Train and predict
framework.fit(data)
predictions, intervals = framework.predict(horizon=180)
```

## Project Structure

```
renewable_energy_forecasting/
├── src/                    # Core implementation
│   ├── data/              # Data loading and preprocessing
│   ├── clustering/        # Dual clustering algorithms
│   ├── neutrosophic/      # Neutrosophic transformations
│   ├── models/            # Forecasting models
│   ├── framework/         # Main framework pipeline
│   ├── evaluation/        # Evaluation metrics and tests
│   └── utils/             # Utilities and helpers
├── experiments/           # Experimental scripts
├── config/               # Configuration files
├── tests/                # Unit and integration tests
└── results/              # Experimental results
```

## Citation

If you use this code in your research, please cite:

```bibtex
@article{neutrosophic_forecasting_2024,
  title={A Neutrosophic Dual Clustering Random Forest Framework for Uncertainty-Aware Renewable Energy Forecasting},
  author={[Authors]},
  journal={IEEE Transactions on Neural Networks and Learning Systems},
  year={2024}
}
```

## License

MIT License - see LICENSE file for details.