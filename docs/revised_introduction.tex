
\section{Introduction}
\label{sec:intr}

The global energy sector is undergoing a significant transformation towards sustainability, driven by the imperative to mitigate climate change and reduce reliance on fossil fuels \cite{sorensen2023recent}. Renewable energy sources, particularly solar and wind power, are central to this shift \cite{hong2020energy}. However, their integration into power grids presents substantial challenges due to the inherent variability and intermittency of these resources, which are strongly influenced by complex meteorological phenomena \cite{iung2023review}. Consequently, the ability to produce accurate and reliable forecasts of renewable energy generation is not merely beneficial but fundamentally essential for maintaining grid stability, optimizing power dispatch, managing energy storage, and ensuring the economic viability of renewable energy projects \cite{basmadjian2023assessing, pierro2022impact}. Precise predictions allow grid operators to anticipate fluctuations in supply, thereby facilitating better balancing of supply and demand, reducing the need for carbon-intensive spinning reserves, and enabling higher penetration levels of clean energy \cite{hong2020energy}. Developing advanced learning systems capable of navigating the complexities of renewable energy data is thus a critical research endeavor, directly contributing to the field of engineering informatics \cite{xu2024copula, zheng2024interpretable}.

Forecasting renewable energy generation is intrinsically challenging due to multiple factors. The primary difficulty arises from the stochastic nature of the underlying physical processes, such as solar irradiance influenced by dynamic cloud cover and atmospheric conditions, and wind speed affected by turbulence and local topography \cite{sorensen2023recent, hong2020energy}. These factors induce significant non-linearity and rapid fluctuations in power output, often exhibiting multi-modal behavior corresponding to distinct operational regimes (e.g., clear sky vs. overcast, low vs. high wind speed) interspersed with periods of gradual transition \cite{iung2023review}. Furthermore, the data acquisition process itself introduces uncertainties through measurement noise and potential data gaps \cite{benti2023forecasting}. Compounding these issues is the inherent uncertainty in predicting the future state of the system, which is crucial for reliable grid operation. Effective forecasting models must therefore not only capture complex temporal dependencies and non-linear relationships but also provide a principled quantification of the uncertainty associated with their predictions \cite{safaei2023adaptation}.

A wide array of methods has been applied to renewable energy forecasting, encompassing statistical time-series models like ARIMA and Exponential Smoothing \cite{wu2021ensemble, alkandari2024solar}, physical models based on numerical weather predictions \cite{bauer2015quiet}, conventional machine learning techniques such as Support Vector Regression (SVR) and Artificial Neural Networks (ANNs) \cite{mahmud2021machine, akhter2019review}, various hybrid approaches \cite{moosavi2019learning, lin2018multi, gu2025explainable}, and more recently, sophisticated deep learning architectures \cite{hu2024temporal, qiu2024novel, wang2024adaptive, xu2025deep, wang2025novel}. While these approaches have achieved varying degrees of success, many exhibit limitations in addressing the combined challenges comprehensively. Statistical models often struggle with the pronounced non-linearities and non-stationarities characteristic of renewable energy data \cite{wu2021ensemble}. While machine learning and deep learning models offer greater capacity for capturing complex patterns, they can be sensitive to data quality issues, risk overfitting, and often lack mechanisms for explicitly and rigorously quantifying prediction uncertainty beyond simple variance measures or parametric assumptions \cite{mahmud2021machine, ibrahim2022machine}. Hybrid models attempt to combine strengths, but often integrate components in an ad-hoc manner or fail to systematically address the uncertainty stemming from data ambiguity and model limitations \cite{moonchai2020short}. A critical gap persists in the development of frameworks that cohesively integrate the identification of operational regimes with a formal, data-driven quantification of the uncertainty associated with these regimes, ultimately leading to more trustworthy forecasts. The role of artificial intelligence in advancing wind power technologies, for instance, highlights this need for comprehensive data-driven solutions \cite{wang2023tracking}.

To bridge this gap, this paper proposes a novel hybrid framework that synergistically combines dual clustering, neutrosophic set theory, and Random Forest regression for enhanced renewable energy forecasting. The framework commences with a data preprocessing stage, followed by a dual clustering approach employing both Fuzzy C-Means (FCM) \cite{bezdek1984fcm} and K-Means algorithms. This dual strategy is designed to capture complementary aspects of the data structure: K-Means identifies distinct operational centroids, while FCM provides fuzzy memberships that reflect gradual transitions and the degree of belongingness to multiple states simultaneously. Central to our approach is the subsequent neutrosophic transformation stage. Here, the rich information from the dual clustering output is mapped into a neutrosophic set representation for each data point, comprising degrees of Truth ($T$), Falsity ($F$), and Indeterminacy ($I$) \cite{alkhazaleh2015neutrosophic}. Specifically, we introduce a rigorous mapping where $T$ and $F$ reflect confidence in the primary identified cluster and its complement, and $I$ is derived from the ambiguity inherent in the FCM membership distribution (e.g., via entropy), providing a principled quantification of uncertainty related to the data point's position within the cluster structure. This enriched feature set, explicitly encoding both state information and its associated structural uncertainty, is then input to a Random Forest model. Random Forests, known for their robustness and ability to model complex non-linear interactions \cite{breiman2001random}, leverage these neutrosophic features to produce accurate point forecasts while inherently benefiting from the uncertainty information embedded in the inputs, facilitating the generation of reliable prediction intervals.

The principal contributions of this research are therefore articulated as follows. First, we propose a novel integrated learning framework specifically designed for uncertainty-aware renewable energy forecasting, combining dual clustering, neutrosophic logic, and ensemble learning in a unique configuration. This enhances engineering information systems by providing a more comprehensive data representation. Second, we introduce a methodologically sound approach for transforming dual clustering outputs into neutrosophic sets, where the Indeterminacy component provides a meaningful, entropy-based quantification of data ambiguity derived from cluster structure, directly addressing limitations in prior uncertainty modeling attempts that often overlook data-inherent structural ambiguity. Third, through extensive and rigorous experiments on four diverse, real-world solar and wind energy datasets, we demonstrate that our framework yields statistically significant improvements in forecasting accuracy and provides enhanced capabilities for uncertainty assessment, including superior prediction intervals as measured by the Winkler Score, when compared to a comprehensive suite of ten benchmarks, including state-of-the-art deep learning models. This offers a more robust and trustworthy tool for practical energy management.

The remainder of this paper is organized as follows. Section~\ref{sec:theoretical_foundations} details the theoretical underpinnings of the proposed framework. Section~\ref{sec:problem_formulation} formalizes the renewable energy forecasting problem. Section~\ref{sec:proposed_frk} provides a comprehensive description of the proposed hybrid framework. Section~\ref{sec:experiments} presents the experimental setup and results. Section~\ref{sec:discussion} discusses the findings and implications of the research. Finally, Section~\ref{sec:conclusion_futurework} concludes the paper and outlines directions for future work.
